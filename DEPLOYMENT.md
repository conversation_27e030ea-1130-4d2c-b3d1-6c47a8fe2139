# QR Redirect Backend V2 - Deployment Guide

## Overview

This guide covers deploying the QR Redirect Backend V2 to Cloudflare Pages with Functions.

## Prerequisites

- Cloudflare account
- Wrangler CLI installed (`npm install -g wrangler`)
- D1 database already set up (same as main QRAnalytica project)

## Configuration

### 1. Update wrangler.toml

Ensure your `wrangler.toml` has the correct database configuration:

```toml
name = "qr-redirect-backend-v2"
compatibility_date = "2025-01-20"
compatibility_flags = ["nodejs_compat"]

[[d1_databases]]
binding = "DB"
database_name = "qranalytica-astro"
database_id = "1bbe802e-9ae6-46e0-a8d7-9c26e65aa9e6"

[vars]
ALLOWED_ORIGINS = "*"
CORS_MAX_AGE = "86400"
```

### 2. Environment Variables

Set production environment variables:

```bash
# For production, set specific allowed origins
wrangler secret put ALLOWED_ORIGINS
# Enter: https://yourdomain.com,https://dashboard.yourdomain.com
```

## Deployment Steps

### 1. Install Dependencies
```bash
npm install
```

### 2. Build the Project
```bash
npm run build
```

### 3. Deploy to Cloudflare Pages
```bash
npx wrangler pages deploy dist
```

### 4. Configure Custom Domain (Optional)

1. Go to Cloudflare Dashboard > Pages
2. Select your project
3. Go to Custom domains
4. Add your custom domain (e.g., `redirect.yourdomain.com`)

## Testing Deployment

### Health Check
```bash
curl https://your-deployment-url.pages.dev/api/health
```

Expected response:
```json
{
  "success": true,
  "status": "healthy",
  "timestamp": "2025-01-20T10:30:00.000Z",
  "database": true
}
```

### QR Code Redirect Test

1. Create a test QR code in your main application
2. Test redirect by ID: `https://your-deployment-url.pages.dev/api/qr/{qr-id}`
3. Test redirect by slug: `https://your-deployment-url.pages.dev/{custom-slug}`

## API Endpoints

The deployed backend provides these endpoints:

- `GET /api/health` - Health check
- `GET /api/qr/{id}` - QR redirect by ID
- `GET /{slug}` - QR redirect by custom slug
- `GET /api/analytics/{id}` - Analytics data

## Monitoring

### Logs
View logs in Cloudflare Dashboard:
1. Go to Pages > Your Project
2. Click on Functions tab
3. View real-time logs

### Analytics
Monitor performance in Cloudflare Analytics dashboard.

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify D1 database ID in wrangler.toml
   - Check database exists and is accessible

2. **CORS Errors**
   - Update ALLOWED_ORIGINS environment variable
   - Ensure origins include protocol (https://)

3. **QR Code Not Found**
   - Verify QR code exists in database
   - Check custom_slug field is populated

### Debug Steps

1. Check health endpoint first
2. Review function logs in Cloudflare dashboard
3. Verify database connectivity
4. Test with known QR code IDs

## Performance Optimization

The backend is optimized for Cloudflare's edge network:

- Smart placement enabled
- Async analytics storage
- Efficient database queries
- Proper caching headers

## Security

- URL validation prevents malicious redirects
- CORS protection
- Input sanitization
- Parameterized database queries

## Rollback

To rollback to a previous version:

```bash
# List deployments
wrangler pages deployment list

# Rollback to specific deployment
wrangler pages deployment rollback [deployment-id]
```
