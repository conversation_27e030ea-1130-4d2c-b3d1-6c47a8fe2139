import type { DeviceInfo, GeoLocation, QRCode, ParsedQRData } from './types';
import { UAParser } from 'ua-parser-js';

/**
 * Parse User-Agent string to extract device, OS, and browser information
 */
export function parseUserAgent(userAgent: string | null): DeviceInfo {
  if (!userAgent) {
    return { device: null, os: null, browser: null };
  }

  // Use UAParser for more accurate parsing
  const parser = new UAParser(userAgent);
  const userAgentData = parser.getResult();

  // Extract device type
  let device = 'Desktop';
  if (userAgentData.device.type === 'mobile') {
    device = 'Mobile';
  } else if (userAgentData.device.type === 'tablet') {
    device = 'Tablet';
  }

  // Extract OS information
  const os = userAgentData.os.name || null;

  // Extract browser information
  const browser = userAgentData.browser.name || null;

  return { device, os, browser };
}

/**
 * Get geolocation data from Cloudflare's request.cf properties
 */
export function getGeoLocation(request: Request): GeoLocation {
  // Get geolocation data with proper sanitization from Cloudflare's cf object
  const cf = request.cf as any; // Type assertion for Cloudflare properties
  const country = (cf?.country as string)?.trim() || null;
  const city = (cf?.city as string)?.trim() || null;
  const timezone = (
    (cf?.timezone as string)?.trim() ||
    Intl.DateTimeFormat().resolvedOptions().timeZone
  ).replace(/[^\w\/+-]/g, "");

  // For latitude and longitude, we would need to use an external service
  // or maintain a database of city coordinates. For now, we'll set them as null
  // and they can be populated later with a geolocation service if needed
  const lat = null;
  const lon = null;

  return { lat, lon, city, country, timezone };
}

/**
 * Get client IP address from request headers
 */
export function getClientIP(request: Request): string {
  // Get IP address with proper fallback handling
  const ip = (
    request.headers.get("CF-Connecting-IP") ||
    request.headers.get("X-Forwarded-For")?.split(",")[0] ||
    "unknown"
  ).trim();

  return ip;
}

/**
 * Get user agent string from request headers
 */
export function getUserAgent(request: Request): string {
  return request.headers.get("user-agent")?.trim() || "unknown";
}

/**
 * Get referer from request headers
 */
export function getReferer(request: Request): string | null {
  return request.headers.get("referer")?.trim() || null;
}

/**
 * Extract all analytics data from request
 */
export function extractAnalyticsData(request: Request) {
  const userAgent = getUserAgent(request);
  const referer = getReferer(request);
  const ip = getClientIP(request);

  // Parse user agent
  const deviceInfo = parseUserAgent(userAgent);

  // Get geolocation data
  const geoLocation = getGeoLocation(request);

  return {
    ip,
    userAgent,
    referer,
    ...deviceInfo,
    ...geoLocation
  };
}

/**
 * Generate a unique ID for analytics records
 */
export function generateAnalyticsId(): string {
  // Simple UUID v4 generation
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Validate if a URL is safe for redirection
 */
export function isValidRedirectUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    // Only allow http and https protocols
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Create CORS headers for responses
 */
export function createCorsHeaders(origin: string | null, allowedOrigins: string): Headers {
  const headers = new Headers();
  
  // Handle CORS
  if (allowedOrigins === '*') {
    headers.set('Access-Control-Allow-Origin', '*');
  } else if (origin && allowedOrigins.split(',').includes(origin)) {
    headers.set('Access-Control-Allow-Origin', origin);
  }
  
  headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  headers.set('Access-Control-Max-Age', '86400');
  
  return headers;
}

/**
 * Parse QR code data to extract redirect URL - supports both old and new formats
 */
export function parseQRCodeData(qrCode: QRCode): ParsedQRData {
  // Handle new schema format first
  if (qrCode.content_type && qrCode.original_url) {
    return {
      url: qrCode.original_url,
      content_type: qrCode.content_type as any,
      original_data: {
        content_type: qrCode.content_type,
        content_data: qrCode.content_data,
        email_address: qrCode.email_address,
        wifi_ssid: qrCode.wifi_ssid,
        phone_number: qrCode.phone_number
      }
    };
  }

  // Handle legacy format - parse JSON from data field
  if (qrCode.data) {
    try {
      const parsedData = JSON.parse(qrCode.data);
      if (parsedData.data && typeof parsedData.data === 'string') {
        return {
          url: parsedData.data,
          content_type: 'url',
          original_data: parsedData
        };
      }
    } catch (error) {
      console.error('Error parsing legacy QR code data:', error);
    }
  }

  // Fallback to redirect_url if available
  if (qrCode.redirect_url) {
    return {
      url: qrCode.redirect_url,
      content_type: 'url',
      original_data: null
    };
  }

  throw new Error('Unable to extract redirect URL from QR code data');
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  message: string, 
  status: number = 400, 
  corsHeaders?: Headers
): Response {
  const headers = new Headers({
    'Content-Type': 'application/json'
  });

  if (corsHeaders) {
    corsHeaders.forEach((value, key) => {
      headers.set(key, value);
    });
  }

  return new Response(
    JSON.stringify({ 
      success: false, 
      error: message 
    }),
    { status, headers }
  );
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse(
  data: any, 
  status: number = 200, 
  corsHeaders?: Headers
): Response {
  const headers = new Headers({
    'Content-Type': 'application/json'
  });

  if (corsHeaders) {
    corsHeaders.forEach((value, key) => {
      headers.set(key, value);
    });
  }

  return new Response(
    JSON.stringify({ 
      success: true, 
      ...data 
    }),
    { status, headers }
  );
}
