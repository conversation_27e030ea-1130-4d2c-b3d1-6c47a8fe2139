import type { APIRoute } from 'astro';
import { storeScanAnalytics, ensureAnalyticsTable, getQRCodeAnalytics } from '../../database';
import { 
  parseUserAgent, 
  getGeoLocation, 
  getClientIP, 
  generateAnalyticsId,
  createCorsHeaders,
  createErrorResponse,
  createSuccessResponse
} from '../../utils';
import type { ScanAnalytics } from '../../types';

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore - Astro runtime types
    const env = locals.runtime?.env;
    const db = env?.DB as D1Database;

    if (!db) {
      return createErrorResponse('D1 database not configured', 503);
    }

    const origin = request.headers.get('Origin');
    const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
    const corsHeaders = createCorsHeaders(origin, allowedOrigins);

    // Parse request body to get test QR code ID
    const body = await request.json();
    const testQRCodeId = body.qr_code_id || 'test-qr-code-id';

    console.log('Testing analytics storage for QR code:', testQRCodeId);

    // Ensure analytics table exists
    const tableReady = await ensureAnalyticsTable(db);
    if (!tableReady) {
      return createErrorResponse('Failed to ensure analytics table exists', 500, corsHeaders);
    }

    // Create test analytics data
    const now = new Date().toISOString();
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const referrer = request.headers.get('Referer');
    const deviceInfo = parseUserAgent(userAgent);
    const geoLocation = getGeoLocation(request);

    const testAnalyticsData: ScanAnalytics = {
      id: generateAnalyticsId(),
      qr_code_id: testQRCodeId,
      scan_time: now,
      ip: clientIP,
      user_agent: userAgent,
      referrer: referrer,
      lat: geoLocation.lat,
      lon: geoLocation.lon,
      city: geoLocation.city,
      country: geoLocation.country,
      device: deviceInfo.device,
      os: deviceInfo.os,
      browser: deviceInfo.browser,
      created_at: now
    };

    // Store test analytics data
    const analyticsResult = await storeScanAnalytics(db, testAnalyticsData);
    
    if (!analyticsResult) {
      return createErrorResponse('Failed to store test analytics', 500, corsHeaders);
    }

    // Retrieve the stored analytics to verify
    const storedAnalytics = await getQRCodeAnalytics(db, testQRCodeId, 5);

    return createSuccessResponse(
      {
        message: 'Test analytics stored successfully',
        test_analytics_id: testAnalyticsData.id,
        stored_count: storedAnalytics.length,
        latest_analytics: storedAnalytics[0] || null
      },
      200,
      corsHeaders
    );

  } catch (error) {
    console.error('Test analytics error:', error);
    return createErrorResponse('Internal server error', 500);
  }
};

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore - Astro runtime types
    const env = locals.runtime?.env;
    const db = env?.DB as D1Database;

    if (!db) {
      return createErrorResponse('D1 database not configured', 503);
    }

    const origin = request.headers.get('Origin');
    const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
    const corsHeaders = createCorsHeaders(origin, allowedOrigins);

    // Check if analytics table exists
    const tableCheck = await db
      .prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='qr_code_scan_analytics'
      `)
      .first();

    // Get total count of analytics records
    let totalCount = 0;
    if (tableCheck) {
      const countResult = await db
        .prepare('SELECT COUNT(*) as count FROM qr_code_scan_analytics')
        .first();
      totalCount = countResult?.count || 0;
    }

    return createSuccessResponse(
      {
        table_exists: !!tableCheck,
        total_analytics_records: totalCount,
        table_info: tableCheck
      },
      200,
      corsHeaders
    );

  } catch (error) {
    console.error('Test analytics info error:', error);
    return createErrorResponse('Internal server error', 500);
  }
};

export const OPTIONS: APIRoute = async ({ request, locals }) => {
  // @ts-ignore - Astro runtime types
  const env = locals.runtime?.env;
  const origin = request.headers.get('Origin');
  const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
  const corsHeaders = createCorsHeaders(origin, allowedOrigins);

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
};
